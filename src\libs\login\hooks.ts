import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { useState } from 'react'
import { toast } from '@/components/Toast'
import { loginSchema, LoginFormData } from './schema'

export const useLoginForm = () => {
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)

  const form = useForm<LoginFormData>({
    resolver: yupResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  })

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true)
    try {
      const res = await fetch('/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      })
      if (!res.ok) {
        let errMsg = 'Đăng nhập thất bại'
        try {
          const err: any = await res.json()
          errMsg = err.message || errMsg
        } catch {}
        throw new Error(errMsg)
      }
      const result: any = await res.json()
      if (result.token) {
        localStorage.setItem('token', result.token)
      }
      toast.success({
        title: 'Đăng nhập thành công!',
        message: 'Chào mừng bạn quay trở lại',
      })
      form.reset()
    } catch (error) {
      toast.error({
        title: 'Đăng nhập thất bại',
        message:
          error instanceof Error
            ? error.message
            : 'Vui lòng kiểm tra lại thông tin đăng nhập',
      })
    } finally {
      setIsLoading(false)
    }
  }

  return {
    form,
    onSubmit,
    showPassword,
    setShowPassword,
    isLoading,
  }
}
