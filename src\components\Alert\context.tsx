'use client'

import { createContext, useContext } from 'react'
import { WithChildren } from 'types/common'

export type AlertStatus = 'error' | 'success' | 'info' | 'warning'

interface AlertContextType {
  status: AlertStatus
}

const AlertContext = createContext<AlertContextType | undefined>(undefined)

export const useAlertContext = () => {
  const context = useContext(AlertContext)
  if (context === undefined) {
    throw new Error('useAlertContext must be used within an AlertProvider')
  }
  return context
}

export const AlertProvider = ({
  children,
  ...props
}: WithChildren<{ status: AlertStatus }>) => {
  return <AlertContext.Provider value={props}>{children}</AlertContext.Provider>
}
