{"extends": ["next/core-web-vitals", "prettier"], "globals": {"tw": true, "chrome": true}, "parserOptions": {"ecmaVersion": 10, "ecmaFeatures": {"jsx": true}, "project": ["./tsconfig.json"], "createDefaultProgram": true}, "rules": {"react/display-name": "off", "react/no-unescaped-entities": "off", "@next/next/no-img-element": "warn", "import/no-anonymous-default-export": "warn"}, "ignorePatterns": ["__mocks__/"], "overrides": [{"files": ["**/*.ts?(x)", "**/*.js?(x)"], "rules": {"react/react-in-jsx-scope": "off", "react/function-component-definition": "off", "no-shadow": "off"}}]}