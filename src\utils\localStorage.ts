import type { UserInfo } from "../types/auth"

const USER_KEY = "userInfo"
const ACCESS_TOKEN_KEY = "accessToken"
const REFRESH_TOKEN_KEY = "refreshToken"
const CART_KEY = "cartItems"

export const localStorageUtils = {
  // User management
  getUser: (): UserInfo | null => {
    if (typeof window === "undefined") return null
    try {
      const storedUser = localStorage.getItem(USER_KEY)
      return storedUser ? (JSON.parse(storedUser) as UserInfo) : null
    } catch (error) {
      console.error("Error getting user from localStorage:", error)
      return null
    }
  },

  setUser(user: UserInfo | null): void {
    if (typeof window === "undefined") return
    try {
      if (user) {
        localStorage.setItem(USER_KEY, JSON.stringify(user))
      } else {
        localStorage.removeItem(USER_KEY)
      }
    } catch (error) {
      console.error("Error setting user in localStorage:", error)
    }
  },

  removeUser: (): void => {
    if (typeof window === "undefined") return
    try {
      localStorage.removeItem(USER_KEY)
    } catch (error) {
      console.error("Error removing user from localStorage:", error)
    }
  },

  // Token management
  getAccessToken: (): string | null => {
    if (typeof window === "undefined") return null
    try {
      return localStorage.getItem(ACCESS_TOKEN_KEY)
    } catch (error) {
      console.error("Error getting access token from localStorage:", error)
      return null
    }
  },

  setAccessToken: (token: string): void => {
    if (typeof window === "undefined") return
    try {
      localStorage.setItem(ACCESS_TOKEN_KEY, token)
    } catch (error) {
      console.error("Error setting access token in localStorage:", error)
    }
  },

  removeAccessToken: (): void => {
    if (typeof window === "undefined") return
    try {
      localStorage.removeItem(ACCESS_TOKEN_KEY)
    } catch (error) {
      console.error("Error removing access token from localStorage:", error)
    }
  },

  getRefreshToken: (): string | null => {
    if (typeof window === "undefined") return null
    try {
      return localStorage.getItem(REFRESH_TOKEN_KEY)
    } catch (error) {
      console.error("Error getting refresh token from localStorage:", error)
      return null
    }
  },

  setRefreshToken: (token: string): void => {
    if (typeof window === "undefined") return
    try {
      localStorage.setItem(REFRESH_TOKEN_KEY, token)
    } catch (error) {
      console.error("Error setting refresh token in localStorage:", error)
    }
  },

  removeRefreshToken: (): void => {
    if (typeof window === "undefined") return
    try {
      localStorage.removeItem(REFRESH_TOKEN_KEY)
    } catch (error) {
      console.error("Error removing refresh token from localStorage:", error)
    }
  },

  // Cart management
  getCartItems: (): any[] => {
    if (typeof window === "undefined") return []
    try {
      const storedCart = localStorage.getItem(CART_KEY)
      return storedCart ? JSON.parse(storedCart) : []
    } catch (error) {
      console.error("Error getting cart items from localStorage:", error)
      return []
    }
  },

  setCartItems: (items: any[]): void => {
    if (typeof window === "undefined") return
    try {
      localStorage.setItem(CART_KEY, JSON.stringify(items))
    } catch (error) {
      console.error("Error setting cart items in localStorage:", error)
    }
  },

  removeCartItems: (): void => {
    if (typeof window === "undefined") return
    try {
      localStorage.removeItem(CART_KEY)
    } catch (error) {
      console.error("Error removing cart items from localStorage:", error)
    }
  },

  // Clear all data
  clearAll: (): void => {
    if (typeof window === "undefined") return
    try {
      localStorage.removeItem(USER_KEY)
      localStorage.removeItem(ACCESS_TOKEN_KEY)
      localStorage.removeItem(REFRESH_TOKEN_KEY)
      localStorage.removeItem(CART_KEY)
    } catch (error) {
      console.error("Error clearing localStorage:", error)
    }
  },

  // Check if localStorage is available
  isAvailable: (): boolean => {
    if (typeof window === "undefined") return false
    try {
      const test = "__localStorage_test__"
      localStorage.setItem(test, test)
      localStorage.removeItem(test)
      return true
    } catch (error) {
      return false
    }
  },
}
