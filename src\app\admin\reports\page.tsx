'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAdminAuthContext } from '../../../context/adminAuth'
import { AdminLayout } from '../../../components/Admin/Layout'
import { Card } from '../../../components/Card'
import { formatNumber } from '../../../utils/number'

export default function ReportsPage() {
  const { isAdminLogin } = useAdminAuthContext()
  const router = useRouter()

  useEffect(() => {
    if (!isAdminLogin) {
      router.push('/login')
    }
  }, [isAdminLogin, router])

  if (!isAdminLogin) return <div>Loading...</div>

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">Báo cáo</h1>
          <p className="mt-1 text-sm text-gray-500">
            Th<PERSON><PERSON> kê doanh thu và hiệu suất kinh doanh
          </p>
        </div>
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          <Card>
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                    <svg
                      className="w-5 h-5 text-green-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                      />
                    </svg>
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Doanh thu tháng này
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {formatNumber(67000000)} ₫
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </Card>
          {/* ... các Card khác ... */}
        </div>
        {/* ... các bảng và thống kê khác ... */}
      </div>
    </AdminLayout>
  )
}
