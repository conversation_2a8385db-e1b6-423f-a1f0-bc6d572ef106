import '../styles/index.css'
import { ReactNode } from 'react'
import { AdminAuthProvider } from '../context/adminAuth'
import { AuthProvider } from '../context/authContext'
import { CartProvider } from '../context/cart'
import { Toaster } from '../components/Toast'

export const metadata = {
  title: 'IBBook - <PERSON>yên sách tiếng <PERSON>h',
  description:
    'IBBook - Chuyên cung cấp sách tiếng <PERSON>h Cambridge, IELTS, Oxford, Pearson với giá tốt nhất. Giao hàng toàn quốc, tặng kèm file nghe miễn phí.',
  openGraph: {
    title: 'IBBook - <PERSON>yên sách tiếng <PERSON>h',
    description:
      'IBBook - <PERSON>yên cung cấp sách tiếng Anh Cambridge, IELTS, Oxford, Pearson với giá tốt nhất. Giao hàng toàn quốc, tặng kèm file nghe miễn phí.',
    images: ['/thumbnail.jpeg'],
  },
  twitter: {
    site: '@sachtienganhhanoi',
    card: 'summary_large_image',
    image: '/thumbnail.jpeg',
  },
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon-16x16.png',
    apple: '/favicon-32x32.png',
  },
  themeColor: '#E13F5E',
}

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="vi">
      <head>
        <link href="/favicon.ico" rel="icon" type="image/x-icon" />
        <link
          href="/favicon-16x16.png"
          rel="icon"
          sizes="16x16"
          type="image/png"
        />
        <link
          href="/favicon-32x32.png"
          rel="icon"
          sizes="32x32"
          type="image/png"
        />
        <link href="https://fonts.googleapis.com" rel="preconnect" />
        <link href="https://static.cloudflareinsights.com" rel="preconnect" />
        <meta content="#E13F5E" name="theme-color" />
        <link href="https://fonts.googleapis.com" rel="preconnect" />
        <link
          crossOrigin="anonymous"
          href="https://fonts.gstatic.com"
          rel="preconnect"
        />
        <link
          href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap"
          rel="stylesheet"
        />
      </head>
      <body>
        <AuthProvider>
          <AdminAuthProvider>
            <CartProvider>{children}</CartProvider>
          </AdminAuthProvider>
        </AuthProvider>
        <Toaster />
      </body>
    </html>
  )
}
