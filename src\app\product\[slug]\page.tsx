'use client'

import { useParams, useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import Image from 'next/image'
import { books } from 'data/books'
import { useCart } from 'context/cart'
import { SEO } from 'components/SEO'

export default function ProductDetailPage() {
  const { slug } = useParams<{ slug: string }>()
  const router = useRouter()
  const { addToCart } = useCart()
  const [quantity, setQuantity] = useState(1)
  const [book, setBook] = useState<any>(null)

  useEffect(() => {
    if (slug) {
      const found = books.find((b) => b.slug === slug)
      setBook(found)
    }
  }, [slug])

  if (!book) {
    return (
      <div className="min-h-[60vh] flex items-center justify-center text-gray-500 text-xl">
        Không tìm thấy sản phẩm
      </div>
    )
  }

  const handleAddToCart = () => {
    addToCart(book, quantity)
    router.push('/cart')
  }

  return (
    <>
      <SEO title={book.title} description={book.description} />
      <div className="max-w-5xl mx-auto px-4 py-10 grid grid-cols-1 md:grid-cols-2 gap-10">
        {/* Hình ảnh */}
        <div className="flex flex-col gap-4">
          <div className="relative w-full h-96 bg-gray-100 rounded-lg overflow-hidden">
            <Image
              src={book.image}
              alt={book.title}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, 50vw"
              priority
            />
          </div>
          <div className="flex gap-2">
            {book.images?.map((img: string, idx: number) => (
              <div
                key={idx}
                className="w-20 h-20 relative rounded overflow-hidden border"
              >
                <Image
                  src={img}
                  alt={book.title}
                  fill
                  className="object-cover"
                />
              </div>
            ))}
          </div>
        </div>
        {/* Thông tin */}
        <div className="flex flex-col gap-6">
          <h1 className="text-3xl font-bold text-gray-900">{book.title}</h1>
          <div className="text-gray-600 text-base">Tác giả: {book.author}</div>
          <div className="flex items-center gap-4">
            <span className="text-2xl font-bold text-blue-600">
              {book.price.toLocaleString('vi-VN')}đ
            </span>
            {book.originalPrice && (
              <span className="text-gray-400 line-through">
                {book.originalPrice.toLocaleString('vi-VN')}đ
              </span>
            )}
            {book.discount && (
              <span className="bg-red-100 text-red-600 px-2 py-1 rounded text-xs font-semibold">
                -{book.discount}%
              </span>
            )}
          </div>
          <div className="flex items-center gap-2 text-yellow-500">
            <span>★ {book.rating}</span>
            <span className="text-gray-500 text-sm">
              ({book.reviewCount} đánh giá)
            </span>
          </div>
          <div className="text-gray-700 text-base whitespace-pre-line">
            {book.description}
          </div>
          <div className="flex items-center gap-4">
            <span>Số lượng:</span>
            <input
              type="number"
              min={1}
              max={book.stockQuantity}
              value={quantity}
              onChange={(e) => setQuantity(Number(e.target.value))}
              className="w-20 border rounded px-2 py-1"
            />
            <span className="text-gray-500 text-sm">
              Còn lại: {book.stockQuantity}
            </span>
          </div>
          <button
            onClick={handleAddToCart}
            className="bg-blue-600 text-white px-6 py-3 rounded-md font-semibold hover:bg-blue-700 transition-colors mt-2"
            disabled={!book.inStock || quantity < 1}
          >
            {book.inStock ? 'Thêm vào giỏ hàng' : 'Hết hàng'}
          </button>
          <div className="text-sm text-gray-500 mt-4">
            <div>Nhà xuất bản: {book.publisher}</div>
            <div>Năm xuất bản: {book.publishYear}</div>
            <div>Ngôn ngữ: {book.language}</div>
            <div>ISBN: {book.isbn}</div>
            <div>Thể loại: {book.category}</div>
            <div>Lớp: {book.gradeLevel}</div>
            <div>Số trang: {book.pages}</div>
          </div>
        </div>
      </div>
    </>
  )
}
