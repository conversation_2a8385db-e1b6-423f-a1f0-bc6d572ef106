import { AxiosError } from 'axios'
import { localStorageUtils } from '../utils/localStorage'

export const handleApiError = (error: AxiosError) => {
  if (error.response) {
    // Server responded with error status
    const { status, data } = error.response

    switch (status) {
      case 401:
        // Unauthorized - redirect to login or refresh token
        localStorageUtils.clearAll()
        if (typeof window !== 'undefined') {
          window.location.href = '/admin/login'
        }
        break
      case 403:
        // Forbidden
        console.error('Access forbidden:', data)
        break
      case 404:
        // Not found
        console.error('Resource not found:', data)
        break
      case 500:
        // Server error
        console.error('Server error:', data)
        break
      default:
        console.error('API Error:', data)
    }

    return Promise.reject(error)
  } else if (error.request) {
    // Network error
    console.error('Network error:', error.message)
    return Promise.reject(
      new Error('Network error. Please check your connection.'),
    )
  } else {
    // Something else happened
    console.error('Error:', error.message)
    return Promise.reject(error)
  }
}
