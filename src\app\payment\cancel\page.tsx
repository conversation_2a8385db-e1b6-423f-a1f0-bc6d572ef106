'use client'

import Link from 'next/link'
import { useSearchParams } from 'next/navigation'
import { Suspense } from 'react'
import { BookStoreLayout } from '../../../components/BookStore/Layout'
import { Button } from '../../../components/Button'

function PaymentCancelContent() {
  const searchParams = useSearchParams()
  const orderId = searchParams.get('orderId')

  return (
    <BookStoreLayout>
      <div className="max-w-4xl mx-auto px-4 py-16">
        <div className="text-center">
          {/* Cancel Icon */}
          <div className="w-24 h-24 mx-auto mb-8 bg-yellow-100 rounded-full flex items-center justify-center">
            <svg
              className="w-12 h-12 text-yellow-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>

          {/* Title and Message */}
          <h1 className="text-4xl font-bold text-yellow-600 mb-4">
            Thanh toán đã bị hủy
          </h1>

          <p className="text-xl text-gray-600 mb-8">
            Bạn đã hủy quá trình thanh toán. Đơn hàng của bạn vẫn được lưu trong
            giỏ hàng.
          </p>

          {/* Order Info */}
          {orderId && (
            <div className="bg-gray-50 rounded-lg p-6 mb-8 max-w-md mx-auto">
              <h3 className="font-semibold text-gray-900 mb-4">
                Thông tin đơn hàng
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Mã đơn hàng:</span>
                  <span className="font-mono font-semibold">#{orderId}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Trạng thái:</span>
                  <span className="font-semibold text-yellow-600">
                    Đã hủy thanh toán
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Reasons */}
          <div className="bg-blue-50 rounded-lg p-6 mb-8 max-w-2xl mx-auto text-left">
            <h3 className="font-semibold text-blue-900 mb-4">
              Một số lý do có thể khiến thanh toán bị hủy:
            </h3>
            <ul className="space-y-2 text-sm text-blue-800">
              <li className="flex items-start space-x-2">
                <span className="text-blue-600 mt-1">•</span>
                <span>
                  Bạn đã nhấn nút "Hủy" hoặc "Quay lại" trên trang thanh toán
                </span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="text-blue-600 mt-1">•</span>
                <span>Phiên thanh toán đã hết hạn (thường là 15 phút)</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="text-blue-600 mt-1">•</span>
                <span>
                  Có lỗi kỹ thuật từ phía ngân hàng hoặc cổng thanh toán
                </span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="text-blue-600 mt-1">•</span>
                <span>
                  Thông tin thẻ không chính xác hoặc thẻ không đủ số dư
                </span>
              </li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <Link href="/checkout">
              <Button size="lg">Thử lại thanh toán</Button>
            </Link>
            <Link href="/cart">
              <Button appearance="secondary" size="lg">
                Quay lại giỏ hàng
              </Button>
            </Link>
            <Link href="/bookstore">
              <Button appearance="secondary" size="lg">
                Tiếp tục mua sắm
              </Button>
            </Link>
          </div>

          {/* Alternative Payment Methods */}
          <div className="bg-green-50 rounded-lg p-6 mb-8 max-w-2xl mx-auto">
            <h3 className="font-semibold text-green-900 mb-4">
              Phương thức thanh toán khác
            </h3>
            <p className="text-sm text-green-800 mb-4">
              Nếu bạn gặp khó khăn với thanh toán online, bạn có thể chọn:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-green-800">
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-green-200 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-xs font-semibold">1</span>
                </div>
                <div>
                  <p className="font-medium">Thanh toán khi nhận hàng (COD)</p>
                  <p className="text-xs">
                    Thanh toán bằng tiền mặt khi nhận hàng
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-green-200 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-xs font-semibold">2</span>
                </div>
                <div>
                  <p className="font-medium">Chuyển khoản ngân hàng</p>
                  <p className="text-xs">
                    Chuyển khoản trực tiếp vào tài khoản
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Support Info */}
          <div className="p-6 bg-gray-50 rounded-lg max-w-md mx-auto">
            <h3 className="font-semibold text-gray-900 mb-2">Cần hỗ trợ?</h3>
            <p className="text-sm text-gray-600 mb-3">
              Nếu bạn cần hỗ trợ về thanh toán, vui lòng liên hệ:
            </p>
            <div className="text-sm text-gray-700">
              <p>📞 Điện thoại: 0949351612</p>
              <p>📧 Email: <EMAIL></p>
              <p>💬 Zalo: IBBook</p>
            </div>
          </div>
        </div>
      </div>
    </BookStoreLayout>
  )
}

export default function PaymentCancelPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <PaymentCancelContent />
    </Suspense>
  )
}
