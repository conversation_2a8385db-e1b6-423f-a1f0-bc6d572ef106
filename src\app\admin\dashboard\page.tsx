'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'

import { useAuth } from '../../../context/authContext'
import { AdminLayout } from '../../../components/Admin/Layout'
import { Card } from '../../../components/Card'
import { formatNumber } from '../../../utils/number'

// Mock data for dashboard
const dashboardStats = {
  totalCustomers: 1247,
  totalOrders: 856,
  totalRevenue: 125600000,
  totalProducts: 14463,
  newCustomersToday: 23,
  ordersToday: 45,
  revenueToday: 8500000,
}

const recentOrders = [
  {
    id: 'ORD-001',
    customer: 'Nguyễn Văn A',
    amount: 450000,
    status: 'completed',
    time: '2 giờ trước',
  },
  {
    id: 'ORD-002',
    customer: 'Trần Thị B',
    amount: 320000,
    status: 'pending',
    time: '3 giờ trước',
  },
  {
    id: 'ORD-003',
    customer: 'Lê Văn C',
    amount: 680000,
    status: 'processing',
    time: '5 giờ trước',
  },
  {
    id: 'ORD-004',
    customer: 'Phạm Thị D',
    amount: 290000,
    status: 'completed',
    time: '1 ngày trước',
  },
]

const topProducts = [
  { name: 'Cambridge IELTS 18', sales: 156, revenue: 26520000 },
  { name: 'Oxford English File', sales: 134, revenue: 5360000 },
  { name: 'Macmillan Gateway', sales: 98, revenue: 11760000 },
  { name: 'Pearson Wider World', sales: 87, revenue: 10440000 },
]

const StatCard = ({
  title,
  value,
  change,
  icon,
}: {
  title: string
  value: string | number
  change?: string
  icon: React.ReactNode
}) => (
  <Card>
    <div className="p-6">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <div className="w-8 h-8 bg-primary-100 rounded-md flex items-center justify-center">
            {icon}
          </div>
        </div>
        <div className="ml-5 w-0 flex-1">
          <dl>
            <dt className="text-sm font-medium text-gray-500 truncate">
              {title}
            </dt>
            <dd className="flex items-baseline">
              <div className="text-2xl font-semibold text-gray-900">
                {typeof value === 'number' ? formatNumber(value) : value}
              </div>
              {change && (
                <div className="ml-2 flex items-baseline text-sm font-semibold text-green-600">
                  +{change}
                </div>
              )}
            </dd>
          </dl>
        </div>
      </div>
    </div>
  </Card>
)

const StatusBadge = ({ status }: { status: string }) => {
  const statusConfig = {
    completed: 'bg-green-100 text-green-800',
    pending: 'bg-yellow-100 text-yellow-800',
    processing: 'bg-blue-100 text-blue-800',
    cancelled: 'bg-red-100 text-red-800',
  }

  const statusText = {
    completed: 'Đã giao',
    pending: 'Chờ xử lý',
    processing: 'Đang xử lý',
    cancelled: 'Đã hủy',
  }

  return (
    <span
      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
        statusConfig[status as keyof typeof statusConfig]
      }`}
    >
      {statusText[status as keyof typeof statusText]}
    </span>
  )
}

export default function AdminDashboard() {
  const { isAuthenticated, isAdmin } = useAuth()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setIsLoading(false)

      if (!isAuthenticated || !isAdmin) {
        router.push('/login')
      }
    }
  }, [isAuthenticated, isAdmin, router])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Đang tải...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated || !isAdmin) {
    return null
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Page header */}
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">
            Bảng điều khiển
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            Tổng quan hoạt động kinh doanh
          </p>
        </div>

        {/* Stats grid */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Tổng khách hàng"
            value={dashboardStats.totalCustomers}
            change={`${dashboardStats.newCustomersToday} hôm nay`}
            icon={
              <svg
                className="w-5 h-5 text-primary-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                />
              </svg>
            }
          />

          <StatCard
            title="Tổng đơn hàng"
            value={dashboardStats.totalOrders}
            change={`${dashboardStats.ordersToday} hôm nay`}
            icon={
              <svg
                className="w-5 h-5 text-primary-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                />
              </svg>
            }
          />

          <StatCard
            title="Doanh thu"
            value={`${formatNumber(dashboardStats.totalRevenue)} ₫`}
            change={`${formatNumber(dashboardStats.revenueToday)} ₫ hôm nay`}
            icon={
              <svg
                className="w-5 h-5 text-primary-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                />
              </svg>
            }
          />

          <StatCard
            title="Tổng sản phẩm"
            value={dashboardStats.totalProducts}
            icon={
              <svg
                className="w-5 h-5 text-primary-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
                />
              </svg>
            }
          />
        </div>

        {/* Recent orders and top products */}
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* Recent orders */}
          <Card>
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Đơn hàng gần đây
              </h3>
              <div className="space-y-4">
                {recentOrders.map((order) => (
                  <div
                    key={order.id}
                    className="flex items-center justify-between"
                  >
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {order.id}
                          </p>
                          <p className="text-sm text-gray-500">
                            {order.customer}
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <StatusBadge status={order.status} />
                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-900">
                          {formatNumber(order.amount)} ₫
                        </p>
                        <p className="text-sm text-gray-500">{order.time}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>

          {/* Top products */}
          <Card>
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Sản phẩm bán chạy
              </h3>
              <div className="space-y-4">
                {topProducts.map((product, index) => (
                  <div
                    key={product.name}
                    className="flex items-center justify-between"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0 w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-primary-600">
                          #{index + 1}
                        </span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {product.name}
                        </p>
                        <p className="text-sm text-gray-500">
                          {product.sales} đã bán
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">
                        {formatNumber(product.revenue)} ₫
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        </div>
      </div>
    </AdminLayout>
  )
}
