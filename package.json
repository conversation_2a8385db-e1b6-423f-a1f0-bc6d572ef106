{"name": "nextjs-boilerplate", "version": "1.1.0", "license": "ISC", "scripts": {"analyze": "ANALYZE=true npm run build", "build": "next build", "clean:install": "rimraf node_modules package-lock.json && npm install", "dev": "next dev", "format": "prettier -c --write \"*/**\"", "lint": "eslint . --ext .ts,.tsx,.js", "lint:fix": "eslint . --ext .ts,.tsx,.js --fix", "prepare": "husky", "release": "standard-version -a", "start": "next start"}, "lint-staged": {"**/*.{json,md}": ["prettier --write"], "**/*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix"]}, "dependencies": {"@hookform/resolvers": "latest", "@next/bundle-analyzer": "^14.2.5", "@radix-ui/react-dialog": "latest", "@radix-ui/react-dropdown-menu": "latest", "@tailwindcss/forms": "latest", "class-variance-authority": "^0.7.1", "classnames": "latest", "clsx": "^2.1.1", "next": "15.2.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "latest", "react-hot-toast": "latest", "tailwind-merge": "^2.5.5", "tailwind-variants": "latest", "yup": "latest"}, "devDependencies": {"@babel/eslint-parser": "^7.22.10", "@svgr/webpack": "^8.1.0", "@total-typescript/ts-reset": "^0.5.1", "@types/node": "^22", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "6.4.0", "@typescript-eslint/parser": "6.4.0", "autoprefixer": "^10.4.20", "axios": "^1.10.0", "dotenv": "^16.4.5", "eslint": "8.30.0", "eslint-config-next": "^13.1.1", "eslint-config-prettier": "9.0.0", "eslint-plugin-prettier": "5.0.0", "husky": "^9.1.4", "lint-staged": "^15.2.8", "postcss": "^8.5", "prettier": "^3.0.0", "rimraf": "^6.0.1", "standard-version": "^9.5.0", "tailwindcss": "3.4.9", "tailwindcss-animate": "^1.0.7", "tsconfig-paths-webpack-plugin": "^4.1.0", "typescript": "^5"}, "engines": {"node": ">=20.0.0", "pnpm": ">=8.2.0"}}