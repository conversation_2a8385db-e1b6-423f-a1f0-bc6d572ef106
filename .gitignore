# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.pnpm-store/

# IDE
.vscode

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store

# environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.development
.env.production
.env.test

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# icons
/src/components/icons/components
/src/components/icons/index.ts
