'use client'

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react'
import { useRouter } from 'next/navigation'
import { authService } from '../services/auth'
import { UserInfo, LoginRequest, RegisterRequest } from '../types/auth'
import { toast } from '@/components/Toast'

interface AuthContextType {
  user: UserInfo | null
  isAuthenticated: boolean
  isAdmin: boolean
  isLoading: boolean
  login: (data: LoginRequest) => Promise<void>
  register: (data: RegisterRequest) => Promise<void>
  logout: () => Promise<void>
  checkAuth: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

interface AuthProviderProps {
  children: ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<UserInfo | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  // Check authentication status on mount
  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = () => {
    if (typeof window === 'undefined') return

    try {
      const currentUser = authService.getCurrentUser()
      const isAuth = authService.isAuthenticated()

      if (isAuth && currentUser) {
        setUser(currentUser)
      } else {
        setUser(null)
      }
    } catch (error) {
      console.error('Auth check error:', error)
      setUser(null)
    } finally {
      setIsLoading(false)
    }
  }

  const login = async (data: LoginRequest) => {
    try {
      setIsLoading(true)

      const response = await authService.login(data)

      setUser(response.user)

      // Redirect based on user role
      if (response.user.role === 'admin') {
        router.push('/admin/dashboard')
      } else {
        // Redirect to home page
        router.push('/')
      }
    } catch (error) {
      console.error('Login error:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (data: RegisterRequest) => {
    try {
      setIsLoading(true)

      // Use real API register
      await authService.register(data)

      // Auto login after successful registration
      await login({ email: data.email, password: data.password })
    } catch (error) {
      console.error('Register error:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    try {
      setIsLoading(true)
      const token = localStorage.getItem('token')
      if (token) {
        const res = await fetch('/auth/logout', {
          method: 'POST',
          headers: { Authorization: `Bearer ${token}` },
        })
        if (!res.ok) {
          let errMsg = 'Đăng xuất thất bại'
          try {
            const err: any = await res.json()
            errMsg = err.message || errMsg
          } catch {}
          toast.error({ title: 'Đăng xuất thất bại', message: errMsg })
        }
      }
      localStorage.removeItem('token')
      setUser(null)
      router.push('/')
    } catch (error) {
      toast.error({
        title: 'Đăng xuất thất bại',
        message: error instanceof Error ? error.message : 'Vui lòng thử lại.',
      })
      console.error('Logout error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isAdmin: user?.role === 'admin',
    isLoading,
    login,
    register,
    logout,
    checkAuth,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
