// Brand Colors - Primary color: #007A9C
export const colors = {
  // Primary brand colors based on #007A9C
  primary: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#007A9C', // Main brand color
    600: '#006b87',
    700: '#005c73',
    800: '#004d5f',
    900: '#003e4b',
    950: '#002832',
  },

  // Secondary colors (complementary to primary)
  secondary: {
    50: '#fef7ee',
    100: '#fdedd3',
    200: '#fbd7a5',
    300: '#f8bc6d',
    400: '#f59532',
    500: '#f2750a',
    600: '#e35d05',
    700: '#bc4508',
    800: '#95370e',
    900: '#792f0f',
    950: '#411505',
  },

  // Accent colors
  accent: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
    950: '#052e16',
  },

  // Neutral grays
  neutral: {
    50: '#fafafa',
    100: '#f5f5f5',
    200: '#e5e5e5',
    300: '#d4d4d4',
    400: '#a3a3a3',
    500: '#737373',
    600: '#525252',
    700: '#404040',
    800: '#262626',
    900: '#171717',
    950: '#0a0a0a',
  },

  // Status colors
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
  },

  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
  },

  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
  },

  info: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
  },
} as const

// Type for color keys
export type ColorKey = keyof typeof colors
export type ColorShade =
  | 50
  | 100
  | 200
  | 300
  | 400
  | 500
  | 600
  | 700
  | 800
  | 900

// Helper functions
export const getColor = (color: ColorKey, shade: ColorShade = 500) => {
  return colors[color][shade] as string
}

// Commonly used colors as constants
export const BRAND_COLORS = {
  PRIMARY: colors.primary[500],
  SECONDARY: colors.secondary[500],
  ACCENT: colors.accent[500],
  SUCCESS: colors.success[500],
  WARNING: colors.warning[500],
  ERROR: colors.error[500],
  INFO: colors.info[500],
} as const
