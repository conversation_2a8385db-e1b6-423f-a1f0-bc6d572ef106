'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAdminAuthContext } from '../../../context/adminAuth'
import { AdminLayout } from '../../../components/Admin/Layout'

interface Program {
  id: string
  name: string
  description: string
  code: string
  isActive: boolean
  categoryCount: number
  bookCount: number
  createdAt: string
  updatedAt: string
}

const mockPrograms: Program[] = [
  {
    id: '1',
    name: 'IB Mathematics',
    description:
      'International Baccalaureate Mathematics program covering Analysis, Approaches, and Applications',
    code: 'IB-MATH',
    isActive: true,
    categoryCount: 4,
    bookCount: 12,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
  },
  // ... các mock khác ...
]

export default function AdminPrograms() {
  const { isAdminLogin } = useAdminAuthContext()
  const router = useRouter()
  const [programs, setPrograms] = useState<Program[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    if (typeof window !== 'undefined' && !isAdminLogin) {
      router.push('/login')
      return
    }
    setTimeout(() => {
      setPrograms(mockPrograms)
      setIsLoading(false)
    }, 500)
  }, [isAdminLogin, router])

  const filteredPrograms = programs.filter(
    (program) =>
      program.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      program.code.toLowerCase().includes(searchTerm.toLowerCase()),
  )

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-semibold text-gray-900">
            Quản lý chương trình
          </h1>
        </div>
        <div className="flex gap-4">
          <input
            type="text"
            placeholder="Tìm kiếm chương trình..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
        </div>
        <div className="bg-white shadow rounded-lg overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tên chương trình
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Mã
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Mô tả
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Số loại sách
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Số sách
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Trạng thái
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Thời gian
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredPrograms.map((program) => (
                <tr key={program.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap font-medium">
                    {program.name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {program.code}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {program.description}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {program.categoryCount}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {program.bookCount}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${program.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}
                    >
                      {program.isActive ? 'Hoạt động' : 'Không hoạt động'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-xs text-gray-500">
                    {new Date(program.createdAt).toLocaleString('vi-VN')}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </AdminLayout>
  )
}
