import * as yup from 'yup'

export const registerSchema = yup.object({
  email: yup.string().required('<PERSON>ail là bắt buộc').email('Email không hợp lệ'),
  phone: yup
    .string()
    .required('<PERSON><PERSON> điện thoại là bắt buộc')
    .matches(/^[0-9]{10,11}$/, 'Số điện thoại không hợp lệ'),
  password: yup
    .string()
    .required('<PERSON>ật khẩu là bắt buộc')
    .min(6, '<PERSON><PERSON>t khẩu phải có ít nhất 6 ký tự'),
})

export type RegisterFormData = yup.InferType<typeof registerSchema>
