export interface UserInfo {
  id: string
  email: string
  phone: string
  address?: string | null
  role: string
  name?: string
  firstName?: string
  lastName?: string
  avatar?: string
  createdAt?: string
  updatedAt?: string
}

export interface LoginRequest {
  email: string
  password: string
}

export interface LoginResponse {
  accessToken: string
  user: UserInfo
}

export interface RegisterRequest {
  name: string
  email: string
  password: string
  confirmPassword: string
}

export interface AdminUser extends UserInfo {
  permissions?: string[]
}
