// Responsive breakpoints
export const breakpoints = {
  xs: '475px',
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
  '3xl': '1920px',
  '4xl': '2560px',
} as const

// Breakpoint values as numbers (for JavaScript usage)
export const breakpointValues = {
  xs: 475,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
  '3xl': 1920,
  '4xl': 2560,
} as const

// Type for breakpoint keys
export type BreakpointKey = keyof typeof breakpoints

// Helper function to get breakpoint value
export const getBreakpoint = (key: BreakpointKey) => breakpoints[key]

// Media query helpers for JavaScript
export const mediaQueries = {
  xs: `(min-width: ${breakpoints.xs})`,
  sm: `(min-width: ${breakpoints.sm})`,
  md: `(min-width: ${breakpoints.md})`,
  lg: `(min-width: ${breakpoints.lg})`,
  xl: `(min-width: ${breakpoints.xl})`,
  '2xl': `(min-width: ${breakpoints['2xl']})`,
  '3xl': `(min-width: ${breakpoints['3xl']})`,
  '4xl': `(min-width: ${breakpoints['4xl']})`,
} as const

// Max-width media queries
export const maxMediaQueries = {
  xs: `(max-width: ${breakpointValues.xs - 1}px)`,
  sm: `(max-width: ${breakpointValues.sm - 1}px)`,
  md: `(max-width: ${breakpointValues.md - 1}px)`,
  lg: `(max-width: ${breakpointValues.lg - 1}px)`,
  xl: `(max-width: ${breakpointValues.xl - 1}px)`,
  '2xl': `(max-width: ${breakpointValues['2xl'] - 1}px)`,
  '3xl': `(max-width: ${breakpointValues['3xl'] - 1}px)`,
  '4xl': `(max-width: ${breakpointValues['4xl'] - 1}px)`,
} as const
