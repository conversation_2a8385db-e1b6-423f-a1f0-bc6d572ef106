// Typography scale
export const fontSize = {
  xxs: ['0.625rem', { lineHeight: '0.875rem' }], // 10px
  tiny: ['0.6875rem', { lineHeight: '1rem' }], // 11px
  xs: ['0.75rem', { lineHeight: '1rem' }], // 12px
  sm: ['0.875rem', { lineHeight: '1.25rem' }], // 14px
  base: ['1rem', { lineHeight: '1.5rem' }], // 16px
  lg: ['1.125rem', { lineHeight: '1.75rem' }], // 18px
  xl: ['1.25rem', { lineHeight: '1.75rem' }], // 20px
  '2xl': ['1.5rem', { lineHeight: '2rem' }], // 24px
  '3xl': ['1.875rem', { lineHeight: '2.25rem' }], // 30px
  '4xl': ['2.25rem', { lineHeight: '2.5rem' }], // 36px
  '5xl': ['3rem', { lineHeight: '1' }], // 48px
  '6xl': ['3.75rem', { lineHeight: '1' }], // 60px
  '7xl': ['4.5rem', { lineHeight: '1' }], // 72px
  '8xl': ['6rem', { lineHeight: '1' }], // 96px
  '9xl': ['8rem', { lineHeight: '1' }], // 128px
  huge: ['10rem', { lineHeight: '1' }], // 160px
  massive: ['12rem', { lineHeight: '1' }], // 192px
} as const

// Font sizes as numbers (for JavaScript usage)
export const fontSizeValues = {
  xxs: 10,
  tiny: 11,
  xs: 12,
  sm: 14,
  base: 16,
  lg: 18,
  xl: 20,
  '2xl': 24,
  '3xl': 30,
  '4xl': 36,
  '5xl': 48,
  '6xl': 60,
  '7xl': 72,
  '8xl': 96,
  '9xl': 128,
  huge: 160,
  massive: 192,
} as const

// Font weights
export const fontWeight = {
  thin: '100',
  extralight: '200',
  light: '300',
  normal: '400',
  medium: '500',
  semibold: '600',
  bold: '700',
  extrabold: '800',
  black: '900',
} as const

// Line heights
export const lineHeight = {
  none: '1',
  tight: '1.25',
  snug: '1.375',
  normal: '1.5',
  relaxed: '1.625',
  loose: '2',
} as const

// Letter spacing
export const letterSpacing = {
  tighter: '-0.05em',
  tight: '-0.025em',
  normal: '0em',
  wide: '0.025em',
  wider: '0.05em',
  widest: '0.1em',
} as const

// Type definitions
export type FontSizeKey = keyof typeof fontSize
export type FontWeightKey = keyof typeof fontWeight
export type LineHeightKey = keyof typeof lineHeight
export type LetterSpacingKey = keyof typeof letterSpacing

// Helper functions
export const getFontSize = (size: FontSizeKey) => fontSize[size]
export const getFontWeight = (weight: FontWeightKey) => fontWeight[weight]
export const getLineHeight = (height: LineHeightKey) => lineHeight[height]
export const getLetterSpacing = (spacing: LetterSpacingKey) =>
  letterSpacing[spacing]
