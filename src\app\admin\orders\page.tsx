'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAdminAuthContext } from '../../../context/adminAuth'
import { AdminLayout } from '../../../components/Admin/Layout'
import { formatNumber } from '../../../utils/number'

interface Order {
  id: string
  customerName: string
  email: string
  phone: string
  address: string
  city: string
  district: string
  ward: string
  totalPrice: number
  status:
    | 'pending'
    | 'processing'
    | 'shipped'
    | 'delivered'
    | 'cancelled'
    | 'pending_payment'
    | 'paid'
    | 'payment_failed'
  paymentMethod: 'direct' | 'online'
  createdAt: string
  updatedAt: string
  items: Array<{
    bookId: string
    title: string
    price: number
    quantity: number
  }>
  notes?: string
}

const mockOrders: Order[] = [
  {
    id: 'ORD1001',
    customerName: 'Nguyễn <PERSON>n <PERSON>',
    email: '<EMAIL>',
    phone: '0901234567',
    address: '123 Đường ABC',
    city: 'Hà Nội',
    district: '<PERSON><PERSON><PERSON>',
    ward: '<PERSON>ị<PERSON>ọng',
    totalPrice: 450000,
    status: 'pending',
    paymentMethod: 'direct',
    createdAt: '2024-07-20T10:30:00Z',
    updatedAt: '2024-07-20T10:30:00Z',
    items: [
      {
        bookId: '1',
        title: "Our World level 4 Student's Book",
        price: 170000,
        quantity: 2,
      },
      {
        bookId: '4',
        title: 'English File Pre-intermediate Workbook',
        price: 40000,
        quantity: 2,
      },
    ],
    notes: 'Giao hàng buổi chiều',
  },
  // ... các mock khác ...
]

const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800'
    case 'processing':
      return 'bg-blue-100 text-blue-800'
    case 'shipped':
      return 'bg-purple-100 text-purple-800'
    case 'delivered':
      return 'bg-green-100 text-green-800'
    case 'cancelled':
      return 'bg-red-100 text-red-800'
    case 'pending_payment':
      return 'bg-orange-100 text-orange-800'
    case 'paid':
      return 'bg-green-100 text-green-800'
    case 'payment_failed':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'pending':
      return 'Chờ xác nhận'
    case 'processing':
      return 'Đang xử lý'
    case 'shipped':
      return 'Đã gửi hàng'
    case 'delivered':
      return 'Đã giao'
    case 'cancelled':
      return 'Đã hủy'
    case 'pending_payment':
      return 'Chờ thanh toán'
    case 'paid':
      return 'Đã thanh toán'
    case 'payment_failed':
      return 'Thanh toán lỗi'
    default:
      return status
  }
}

export default function AdminOrdersPage() {
  const { isAdminLogin } = useAdminAuthContext()
  const router = useRouter()
  const [orders] = useState<Order[]>(mockOrders)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')

  useEffect(() => {
    if (!isAdminLogin) {
      router.push('/login')
    }
  }, [isAdminLogin, router])

  const filteredOrders = orders.filter((order) => {
    const matchesSearch =
      order.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.id.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = !statusFilter || order.status === statusFilter
    return matchesSearch && matchesStatus
  })

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-semibold text-gray-900">
            Quản lý đơn hàng
          </h1>
        </div>
        <div className="flex gap-4">
          <input
            type="text"
            placeholder="Tìm kiếm theo tên, email, mã đơn..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg"
          >
            <option value="">Tất cả trạng thái</option>
            <option value="pending">Chờ xác nhận</option>
            <option value="processing">Đang xử lý</option>
            <option value="shipped">Đã gửi hàng</option>
            <option value="delivered">Đã giao</option>
            <option value="cancelled">Đã hủy</option>
            <option value="pending_payment">Chờ thanh toán</option>
            <option value="paid">Đã thanh toán</option>
            <option value="payment_failed">Thanh toán lỗi</option>
          </select>
        </div>
        <div className="bg-white shadow rounded-lg overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Mã đơn
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Khách hàng
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Email
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Số điện thoại
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tổng tiền
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Trạng thái
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Thời gian
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredOrders.map((order) => (
                <tr key={order.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap font-mono">
                    {order.id}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {order.customerName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">{order.email}</td>
                  <td className="px-6 py-4 whitespace-nowrap">{order.phone}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {formatNumber(order.totalPrice)}₫
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}
                    >
                      {getStatusText(order.status)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-xs text-gray-500">
                    {new Date(order.createdAt).toLocaleString('vi-VN')}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </AdminLayout>
  )
}
