'use client'

import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { BookStoreLayout } from '../../components/BookStore/Layout'
import { SEO } from '../../components/SEO'
import { useCart } from '../../context/cart'
import Image from 'next/image'
import { useAuth } from '../../context/authContext'

export default function CartPage() {
  const router = useRouter()
  const { items, removeFromCart, updateQuantity, clearCart } = useCart()
  const { isAuthenticated } = useAuth()
  const [isUpdating, setIsUpdating] = useState(false)

  const total = items.reduce(
    (sum, item) => sum + item.book.price * item.quantity,
    0,
  )

  const handleQuantityChange = async (bookId: string, newQuantity: number) => {
    setIsUpdating(true)
    updateQuantity(bookId, newQuantity)
    setIsUpdating(false)
  }

  const handleRemoveItem = (bookId: string) => {
    removeFromCart(bookId)
  }

  const handleClearCart = () => {
    if (confirm('<PERSON>ạn có chắc chắn muốn xóa tất cả sản phẩm trong giỏ hàng?')) {
      clearCart()
    }
  }

  if (items.length === 0) {
    return (
      <>
        <SEO
          title="Giỏ hàng trống"
          description="Giỏ hàng của bạn đang trống."
        />
        <BookStoreLayout>
          <div className="max-w-4xl mx-auto px-4 py-8">
            <div className="text-center">
              <h1 className="text-2xl font-bold text-gray-900 mb-4">
                Giỏ hàng trống
              </h1>
              <p className="text-gray-600 mb-8">
                Bạn chưa có sản phẩm nào trong giỏ hàng.
              </p>
              <button
                onClick={() => router.push('/bookstore')}
                className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition-colors"
              >
                Tiếp tục mua sắm
              </button>
            </div>
          </div>
        </BookStoreLayout>
      </>
    )
  }

  return (
    <>
      <SEO title="Giỏ hàng" description="Xem và quản lý giỏ hàng của bạn." />
      <BookStoreLayout>
        <div className="max-w-6xl mx-auto px-4 py-8">
          {!isAuthenticated && (
            <div className="mb-6">
              <div className="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-800 p-4 rounded shadow">
                <strong>Chú ý:</strong> Bạn chưa đăng nhập. Giỏ hàng của bạn chỉ
                được lưu trên thiết bị này. Đăng nhập để đồng bộ và lưu trữ đơn
                hàng.
              </div>
            </div>
          )}
          <h1 className="text-3xl font-bold text-gray-900 mb-8">Giỏ hàng</h1>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Cart Items */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-lg shadow-md">
                <div className="p-6 border-b border-gray-200">
                  <div className="flex justify-between items-center">
                    <h2 className="text-xl font-semibold">
                      Sản phẩm ({items.length})
                    </h2>
                    <button
                      onClick={handleClearCart}
                      className="text-red-600 hover:text-red-800 text-sm"
                    >
                      Xóa tất cả
                    </button>
                  </div>
                </div>

                <div className="divide-y divide-gray-200">
                  {items.map((item) => (
                    <div key={item.book.id} className="p-6">
                      <div className="flex items-center space-x-4">
                        <Image
                          src={item.book.image}
                          alt={item.book.title}
                          className="w-20 h-20 object-cover rounded"
                        />
                        <div className="flex-1">
                          <h3 className="text-lg font-medium text-gray-900">
                            {item.book.title}
                          </h3>
                          <p className="text-gray-600 text-sm">
                            Tác giả: {item.book.author}
                          </p>
                          <p className="text-blue-600 font-semibold mt-1">
                            {item.book.price.toLocaleString('vi-VN')}đ
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <label className="text-sm text-gray-700">
                            Số lượng:
                          </label>
                          <select
                            value={item.quantity}
                            onChange={(e) =>
                              handleQuantityChange(
                                item.book.id,
                                parseInt(e.target.value),
                              )
                            }
                            disabled={isUpdating}
                            className="border border-gray-300 rounded px-2 py-1 text-sm"
                          >
                            {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((num) => (
                              <option key={num} value={num}>
                                {num}
                              </option>
                            ))}
                          </select>
                        </div>
                        <div className="text-right">
                          <p className="text-lg font-semibold text-gray-900">
                            {(item.book.price * item.quantity).toLocaleString(
                              'vi-VN',
                            )}
                            đ
                          </p>
                          <button
                            onClick={() => handleRemoveItem(item.book.id)}
                            className="text-red-600 hover:text-red-800 text-sm mt-1"
                          >
                            Xóa
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-md p-6 sticky top-4">
                <h2 className="text-xl font-semibold mb-4">Tóm tắt đơn hàng</h2>

                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>Tạm tính:</span>
                    <span>{total.toLocaleString('vi-VN')}đ</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Phí vận chuyển:</span>
                    <span>30,000đ</span>
                  </div>
                  <div className="border-t pt-3">
                    <div className="flex justify-between font-bold text-lg">
                      <span>Tổng cộng:</span>
                      <span>{(total + 30000).toLocaleString('vi-VN')}đ</span>
                    </div>
                  </div>
                </div>

                <button
                  onClick={() => router.push('/checkout')}
                  className="w-full bg-blue-600 text-white py-3 rounded-md hover:bg-blue-700 transition-colors font-medium mt-6"
                >
                  Tiến hành thanh toán
                </button>

                <button
                  onClick={() => router.push('/bookstore')}
                  className="w-full border border-gray-300 text-gray-700 py-3 rounded-md hover:bg-gray-50 transition-colors font-medium mt-3"
                >
                  Tiếp tục mua sắm
                </button>
              </div>
            </div>
          </div>
        </div>
      </BookStoreLayout>
    </>
  )
}
