import axios, { type AxiosError, type InternalAxiosRequestConfig } from "axios"
import { handleApiError } from "./apiErrorHandler"
import { localStorageUtils } from "../utils/localStorage"

export interface ApiResponse<T> {
  data: T
  message: string
  success: boolean
}

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  name: string
  email: string
  password: string
  confirmPassword: string
}

export interface User {
  id: string
  name: string
  email: string
  avatar?: string
  role: "user" | "admin"
}

const getToken = (): string | null => {
  return localStorageUtils.getAccessToken()
}

// Create axios instance with default config
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || "http://localhost:3000",
  headers: {
    "Content-Type": "application/json",
  },
})

// Request interceptor
api.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = getToken() // Use the getToken function
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    return config
  },
  (error: AxiosError) => {
    return Promise.reject(error)
  },
)

// Response interceptor
api.interceptors.response.use((response) => response, handleApiError)

// Mock delay to simulate network requests
const mockDelay = (ms = 1000) => new Promise((resolve) => setTimeout(resolve, ms))

// Mock services for now
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3001/api"

export const apiClient = {
  // Authentication endpoints
  auth: {
    login: async (credentials: LoginRequest) => {
      await mockDelay()

      // Replace with actual API call
      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(credentials),
      })
      return response.json()

      // Mock response
      // if (credentials.email === "<EMAIL>" && credentials.password === "admin123") {
      //   return {
      //     success: true,
      //     message: "Login successful",
      //     data: {
      //       user: {
      //         id: "1",
      //         name: "Admin User",
      //         email: credentials.email,
      //         role: "admin",
      //       },
      //       token: "mock-jwt-token",
      //     },
      //   }
      // }

      // throw new Error("Invalid credentials")
    },

    register: async (userData: RegisterRequest) => {
      await mockDelay()

      // Replace with actual API call
      const response = await fetch(`${API_BASE_URL}/auth/register`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(userData),
      })
      return response.json()

      // return {
      //   success: true,
      //   message: "Registration successful",
      //   data: {
      //     user: {
      //       id: "2",
      //       name: userData.name,
      //       email: userData.email,
      //       role: "user",
      //     },
      //     token: "mock-jwt-token",
      //   },
      // }
    },

    logout: async () => {
      await mockDelay(500)

      // Replace with actual API call
      const token = getToken()
      if (token) {
        const response = await fetch(`${API_BASE_URL}/auth/logout`, {
          method: "POST",
          headers: { Authorization: `Bearer ${token}` },
        })
        return response.json()
      }

      return {
        success: true,
        message: "Logout successful",
        data: null,
      }
    },
  },

  // Books/products endpoints
  books: {
    getAll: async (params?: { category?: string; search?: string; page?: number; limit?: number }) => {
      await mockDelay()

      // Replace with actual API call
      const queryParams = new URLSearchParams(params as any)
      const response = await fetch(`${API_BASE_URL}/books?${queryParams}`)
      return response.json()

      // Mock response - books are imported from data file
      // return {
      //   success: true,
      //   message: "Books fetched successfully",
      //   data: [], // Will be replaced with actual data
      // }
    },

    getById: async (id: string) => {
      await mockDelay()

      // Replace with actual API call
      const response = await fetch(`${API_BASE_URL}/books/${id}`)
      return response.json()

      // return {
      //   success: true,
      //   message: "Book fetched successfully",
      //   data: null,
      // }
    },

    create: async (bookData: any) => {
      await mockDelay()

      // Replace with actual API call
      const token = getToken()
      const response = await fetch(`${API_BASE_URL}/books`, {
        method: "POST",
        headers: { "Content-Type": "application/json", Authorization: `Bearer ${token}` },
        body: JSON.stringify(bookData),
      })
      return response.json()

      // return {
      //   success: true,
      //   message: "Book created successfully",
      //   data: { id: Date.now(), ...bookData },
      // }
    },

    update: async (id: string, bookData: any) => {
      await mockDelay()

      // Replace with actual API call
      const token = getToken()
      const response = await fetch(`${API_BASE_URL}/books/${id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json", Authorization: `Bearer ${token}` },
        body: JSON.stringify(bookData),
      })
      return response.json()

      // return {
      //   success: true,
      //   message: "Book updated successfully",
      //   data: { id, ...bookData },
      // }
    },

    delete: async (id: string) => {
      await mockDelay()

      // Replace with actual API call
      const token = getToken()
      const response = await fetch(`${API_BASE_URL}/books/${id}`, {
        method: "DELETE",
        headers: { Authorization: `Bearer ${token}` },
      })
      return response.json()

      // return {
      //   success: true,
      //   message: "Book deleted successfully",
      //   data: null,
      // }
    },
  },

  // Orders endpoints
  orders: {
    create: async (orderData: any) => {
      await mockDelay()

      // Replace with actual API call
      const token = getToken()
      const response = await fetch(`${API_BASE_URL}/orders`, {
        method: "POST",
        headers: { "Content-Type": "application/json", Authorization: `Bearer ${token}` },
        body: JSON.stringify(orderData),
      })
      return response.json()

      // return {
      //   success: true,
      //   message: "Order created successfully",
      //   data: {
      //     orderId: `ORD-${Date.now()}`,
      //     status: "pending",
      //     ...orderData,
      //   },
      // }
    },

    getById: async (id: string) => {
      await mockDelay()

      // Replace with actual API call
      const token = getToken()
      const response = await fetch(`${API_BASE_URL}/orders/${id}`, {
        headers: { Authorization: `Bearer ${token}` },
      })
      return response.json()

      // return {
      //   success: true,
      //   message: "Order fetched successfully",
      //   data: {
      //     id,
      //     status: "confirmed",
      //     items: [],
      //     total: 0,
      //   },
      // }
    },

    getAll: async (params?: { page?: number; status?: string }) => {
      await mockDelay()

      // Replace with actual API call
      const queryParams = new URLSearchParams(params as any)
      const token = getToken()
      const response = await fetch(`${API_BASE_URL}/orders?${queryParams}`, {
        headers: { Authorization: `Bearer ${token}` },
      })
      return response.json()

      // return {
      //   success: true,
      //   message: "Orders fetched successfully",
      //   data: [],
      //   pagination: { page: 1, total: 0, pages: 1 },
      // }
    },
  },

  // Payment endpoints
  payments: {
    process: async (paymentData: any) => {
      await mockDelay(2000) // Longer delay for payment processing

      // Replace with actual payment gateway integration
      // This could be Stripe, PayPal, VNPay, etc.
      const token = getToken()
      const response = await fetch(`${API_BASE_URL}/payments/process`, {
        method: "POST",
        headers: { "Content-Type": "application/json", Authorization: `Bearer ${token}` },
        body: JSON.stringify(paymentData),
      })
      return response.json()

      // return {
      //   success: true,
      //   message: "Payment processed successfully",
      //   data: {
      //     transactionId: `TXN-${Date.now()}`,
      //     status: "completed",
      //   },
      // }
    },

    getStatus: async (transactionId: string) => {
      await mockDelay()

      // Replace with actual API call
      const token = getToken()
      const response = await fetch(`${API_BASE_URL}/payments/${transactionId}`, {
        headers: { Authorization: `Bearer ${token}` },
      })
      return response.json()

      // return {
      //   success: true,
      //   message: "Payment status fetched successfully",
      //   data: {
      //     transactionId,
      //     status: "completed",
      //     amount: 0,
      //   },
      // }
    },
  },

  // Admin endpoints
  admin: {
    getDashboardStats: async () => {
      await mockDelay()

      // Replace with actual API call
      const token = getToken()
      const response = await fetch(`${API_BASE_URL}/admin/dashboard`, {
        headers: { Authorization: `Bearer ${token}` },
      })
      return response.json()

      // return {
      //   success: true,
      //   message: "Dashboard stats fetched successfully",
      //   data: {
      //     totalBooks: 0,
      //     totalOrders: 0,
      //     totalRevenue: 0,
      //     totalCustomers: 0,
      //   },
      // }
    },

    getCustomers: async (params?: { page?: number; search?: string }) => {
      await mockDelay()

      // Replace with actual API call
      const queryParams = new URLSearchParams(params as any)
      const token = getToken()
      const response = await fetch(`${API_BASE_URL}/admin/customers?${queryParams}`, {
        headers: { Authorization: `Bearer ${token}` },
      })
      return response.json()

      // return {
      //   success: true,
      //   message: "Customers fetched successfully",
      //   data: [],
      //   pagination: { page: 1, total: 0, pages: 1 },
      // }
    },
  },
}

export const authService = {
  // Login user with email and password
  login: async (email: string, password: string) => {
    // Mock implementation - replace with actual API call
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          user: { id: "1", email, name: "John Doe" },
          token: "mock-jwt-token",
        })
      }, 1000)
    })
  },

  // Register new user
  register: async (userData: any) => {
    // Mock implementation - replace with actual API call
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          user: { id: "2", ...userData },
          token: "mock-jwt-token",
        })
      }, 1000)
    })
  },

  // Logout user
  logout: async () => {
    // Mock implementation - replace with actual API call
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ success: true })
      }, 500)
    })
  },
}

export const booksService = {
  // Get all books with pagination and filters
  getBooks: async (params?: any) => {
    // Mock implementation - replace with actual API call
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          books: [], // Will be replaced with actual book data
          total: 0,
          page: 1,
          limit: 10,
        })
      }, 800)
    })
  },

  // Get single book by slug
  getBookBySlug: async (slug: string) => {
    // Mock implementation - replace with actual API call
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          book: null, // Will be replaced with actual book data
        })
      }, 600)
    })
  },

  // Search books
  searchBooks: async (query: string) => {
    // Mock implementation - replace with actual API call
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          books: [], // Will be replaced with search results
          total: 0,
        })
      }, 700)
    })
  },
}

export const cartService = {
  // Get user's cart
  getCart: async () => {
    // Mock implementation - replace with actual API call
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          items: [], // Will be replaced with actual cart items
          total: 0,
        })
      }, 500)
    })
  },

  // Add item to cart
  addToCart: async (bookId: string, quantity = 1) => {
    // Mock implementation - replace with actual API call
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: "Item added to cart",
        })
      }, 600)
    })
  },

  // Update cart item quantity
  updateCartItem: async (itemId: string, quantity: number) => {
    // Mock implementation - replace with actual API call
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: "Cart updated",
        })
      }, 400)
    })
  },

  // Remove item from cart
  removeFromCart: async (itemId: string) => {
    // Mock implementation - replace with actual API call
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: "Item removed from cart",
        })
      }, 400)
    })
  },
}

export const ordersService = {
  // Create new order
  createOrder: async (orderData: any) => {
    // Mock implementation - replace with actual API call
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          order: {
            id: "ORDER-" + Date.now(),
            status: "pending",
            total: orderData.total,
          },
        })
      }, 1000)
    })
  },

  // Get user's orders
  getOrders: async () => {
    // Mock implementation - replace with actual API call
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          orders: [], // Will be replaced with actual orders
        })
      }, 800)
    })
  },

  // Get single order by ID
  getOrderById: async (orderId: string) => {
    // Mock implementation - replace with actual API call
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          order: null, // Will be replaced with actual order data
        })
      }, 600)
    })
  },
}

export const paymentService = {
  // Process payment
  processPayment: async (paymentData: any) => {
    // Mock implementation - replace with actual payment gateway integration
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          transactionId: "TXN-" + Date.now(),
          message: "Payment processed successfully",
        })
      }, 2000)
    })
  },

  // Verify payment status
  verifyPayment: async (transactionId: string) => {
    // Mock implementation - replace with actual payment verification
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          status: "completed",
          transactionId,
        })
      }, 1000)
    })
  },
}

export const adminService = {
  // Get dashboard statistics
  getDashboardStats: async () => {
    // Mock implementation - replace with actual API call
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          totalBooks: 0,
          totalOrders: 0,
          totalRevenue: 0,
          totalCustomers: 0,
        })
      }, 800)
    })
  },

  // Manage books (CRUD operations)
  manageBooks: {
    create: async (bookData: any) => {
      // Create new book
      return new Promise((resolve) => {
        setTimeout(() => resolve({ success: true }), 1000)
      })
    },
    update: async (bookId: string, bookData: any) => {
      // Update existing book
      return new Promise((resolve) => {
        setTimeout(() => resolve({ success: true }), 800)
      })
    },
    delete: async (bookId: string) => {
      // Delete book
      return new Promise((resolve) => {
        setTimeout(() => resolve({ success: true }), 600)
      })
    },
  },
}

export default {
  apiClient,
  authService,
  booksService,
  cartService,
  ordersService,
  paymentService,
  adminService,
}
