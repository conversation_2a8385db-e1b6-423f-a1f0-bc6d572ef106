import api from './api'
import { localStorageUtils } from '../utils/localStorage'
import {
  UserInfo,
  LoginRequest,
  LoginResponse,
  RegisterRequest,
} from '../types/auth'

export interface RegisterResponse {
  message: string
  user: UserInfo
}

export const authService = {
  // Register new user
  register: async (data: RegisterRequest): Promise<RegisterResponse> => {
    try {
      const response = await api.post('/auth/register', data)
      return response.data
    } catch (error) {
      throw error
    }
  },

  // Login user
  login: async (data: LoginRequest): Promise<LoginResponse> => {
    try {
      const response = await api.post('/auth/login', data)
      const { accessToken, user } = response.data

      // Store token and user info
      localStorageUtils.setAccessToken(accessToken)
      localStorageUtils.setUser(user)

      return response.data
    } catch (error) {
      throw error
    }
  },

  // Logout user
  logout: async (): Promise<void> => {
    try {
      // Call logout endpoint if needed
      await api.post('/auth/logout')
    } catch (error) {
      // Continue with logout even if API call fails
      console.error('Logout API error:', error)
    } finally {
      // Clear local storage
      localStorageUtils.clearAll()
    }
  },

  // Get current user from token
  getCurrentUser: (): UserInfo | null => {
    return localStorageUtils.getUser()
  },

  // Check if user is authenticated
  isAuthenticated: (): boolean => {
    const token = localStorageUtils.getAccessToken()
    const user = localStorageUtils.getUser()
    return !!(token && user)
  },

  // Check if user is admin
  isAdmin: (): boolean => {
    const user = localStorageUtils.getUser()
    return user?.role === 'admin'
  },

  // Verify token validity
  verifyToken: async (): Promise<boolean> => {
    try {
      const response = await api.get('/auth/verify')
      return response.status === 200
    } catch (error) {
      return false
    }
  },
}
