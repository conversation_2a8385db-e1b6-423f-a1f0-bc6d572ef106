'use client'

import { useEffect, useState, Suspense } from 'react'
import Link from 'next/link'
import { useSearchParams } from 'next/navigation'

import { BookStoreLayout } from '../../../components/BookStore/Layout'
import { Button } from '../../../components/Button'

function PaymentReturnContent() {
  const [isProcessing, setIsProcessing] = useState(true)
  const searchParams = useSearchParams()

  const orderId = searchParams.get('orderId')
  const status = searchParams.get('status')
  const transactionId = searchParams.get('transactionId')

  useEffect(() => {
    const verifyPayment = async () => {
      if (orderId && transactionId) {
        try {
          const { paymentService } = await import('../../../services/api')
          await paymentService.verifyPayment(transactionId)

          // Payment verification completed
          setIsProcessing(false)
        } catch (error) {
          console.error('Payment verification error:', error)
          setIsProcessing(false)
        }
      } else {
        // No payment data, just stop processing
        const timer = setTimeout(() => {
          setIsProcessing(false)
        }, 1000)
        return () => clearTimeout(timer)
      }
    }

    verifyPayment()
  }, [orderId, transactionId])

  if (isProcessing) {
    return (
      <BookStoreLayout>
        <div className="max-w-4xl mx-auto px-4 py-16">
          <div className="text-center">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary-500 mx-auto mb-8"></div>
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              Đang xử lý thanh toán...
            </h1>
            <p className="text-gray-600">Vui lòng đợi trong giây lát</p>
          </div>
        </div>
      </BookStoreLayout>
    )
  }

  const isSuccess = status === 'success'

  return (
    <BookStoreLayout>
      <div className="max-w-4xl mx-auto px-4 py-16">
        <div className="text-center">
          {/* Success/Failure Icon */}
          <div
            className={`w-24 h-24 mx-auto mb-8 rounded-full flex items-center justify-center ${
              isSuccess ? 'bg-green-100' : 'bg-red-100'
            }`}
          >
            {isSuccess ? (
              <svg
                className="w-12 h-12 text-green-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            ) : (
              <svg
                className="w-12 h-12 text-red-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            )}
          </div>

          {/* Title and Message */}
          <h1
            className={`text-4xl font-bold mb-4 ${
              isSuccess ? 'text-green-600' : 'text-red-600'
            }`}
          >
            {isSuccess ? 'Thanh toán thành công!' : 'Thanh toán thất bại!'}
          </h1>

          <p className="text-xl text-gray-600 mb-8">
            {isSuccess
              ? 'Cảm ơn bạn đã thanh toán. Đơn hàng của bạn đã được xác nhận.'
              : 'Có lỗi xảy ra trong quá trình thanh toán. Vui lòng thử lại.'}
          </p>

          {/* Order Details */}
          {orderId && (
            <div className="bg-gray-50 rounded-lg p-6 mb-8 max-w-md mx-auto">
              <h3 className="font-semibold text-gray-900 mb-4">
                Thông tin giao dịch
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Mã đơn hàng:</span>
                  <span className="font-mono font-semibold">#{orderId}</span>
                </div>
                {transactionId && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Mã giao dịch:</span>
                    <span className="font-mono font-semibold">
                      #{transactionId}
                    </span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-gray-600">Trạng thái:</span>
                  <span
                    className={`font-semibold ${
                      isSuccess ? 'text-green-600' : 'text-red-600'
                    }`}
                  >
                    {isSuccess ? 'Thành công' : 'Thất bại'}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            {isSuccess ? (
              <>
                <Link href={`/order-success?orderId=${orderId}`}>
                  <Button size="lg">Xem chi tiết đơn hàng</Button>
                </Link>
                <Link href="/bookstore">
                  <Button appearance="secondary" size="lg">
                    Tiếp tục mua sắm
                  </Button>
                </Link>
              </>
            ) : (
              <>
                <Link href="/checkout">
                  <Button size="lg">Thử lại thanh toán</Button>
                </Link>
                <Link href="/cart">
                  <Button appearance="secondary" size="lg">
                    Quay lại giỏ hàng
                  </Button>
                </Link>
              </>
            )}
          </div>

          {/* Support Info */}
          <div className="mt-12 p-6 bg-blue-50 rounded-lg max-w-md mx-auto">
            <h3 className="font-semibold text-blue-900 mb-2">Cần hỗ trợ?</h3>
            <p className="text-sm text-blue-800 mb-3">
              Nếu bạn gặp vấn đề với thanh toán, vui lòng liên hệ với chúng tôi:
            </p>
            <div className="text-sm text-blue-800">
              <p>📞 Điện thoại: 0949351612</p>
              <p>📧 Email: <EMAIL></p>
              <p>💬 Zalo: IBBook</p>
            </div>
          </div>
        </div>
      </div>
    </BookStoreLayout>
  )
}

export default function PaymentReturnPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <PaymentReturnContent />
    </Suspense>
  )
}
