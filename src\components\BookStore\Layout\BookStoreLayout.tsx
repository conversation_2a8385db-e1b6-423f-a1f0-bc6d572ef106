import type React from "react"
import { <PERSON><PERSON> } from "./Header"
import { Footer } from "./Footer"

interface BookStoreLayoutProps {
  children: React.ReactNode
}

export const BookStoreLayout: React.FC<BookStoreLayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <Header />
      <main className="flex-1">{children}</main>
      <Footer />
    </div>
  )
}
