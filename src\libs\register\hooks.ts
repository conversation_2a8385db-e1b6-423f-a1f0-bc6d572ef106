import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { useState } from 'react'
import { toast } from '@/components/Toast'
import { registerSchema, RegisterFormData } from './schema'

export const useRegisterForm = () => {
  const [isLoading, setIsLoading] = useState(false)

  const form = useForm<RegisterFormData>({
    resolver: yupResolver(registerSchema),
    defaultValues: {
      email: '',
      phone: '',
      password: '',
    },
  })

  const onSubmit = async (data: RegisterFormData) => {
    setIsLoading(true)
    try {
      const res = await fetch('/auth/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      })
      if (!res.ok) {
        const err: any = await res.json()
        throw new Error(err.message || 'Đăng ký thất bại')
      }
      toast.success({
        title: '<PERSON><PERSON><PERSON> ký thành công!',
        message: 'T<PERSON><PERSON> khoản của bạn đã được tạo thành công',
      })
      form.reset()
    } catch (error) {
      toast.error({
        title: 'Đăng ký thất bại',
        message:
          error instanceof Error
            ? error.message
            : 'Vui lòng kiểm tra lại thông tin đăng ký',
      })
    } finally {
      setIsLoading(false)
    }
  }

  return {
    form,
    onSubmit,
    isLoading,
  }
}
