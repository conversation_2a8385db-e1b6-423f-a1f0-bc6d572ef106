'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAdminAuthContext } from '../../../context/adminAuth'
import { AdminLayout } from '../../../components/Admin/Layout'

interface Payment {
  id: string
  orderId: string
  userId: string
  userName: string
  userEmail: string
  amount: number
  method: 'direct' | 'online' | '9pay' | 'momo' | 'vnpay'
  status: 'pending' | 'completed' | 'failed' | 'refunded'
  transactionId?: string
  gatewayResponse?: string
  createdAt: string
  updatedAt: string
}

const mockPayments: Payment[] = [
  {
    id: '1',
    orderId: 'ORD-001',
    userId: '1',
    userName: 'Nguyễn Văn A',
    userEmail: '<EMAIL>',
    amount: 450000,
    method: '9pay',
    status: 'completed',
    transactionId: '9PAY-TXN-123456',
    gatewayResponse: 'Payment successful',
    createdAt: '2024-01-20T14:30:00Z',
    updatedAt: '2024-01-20T14:35:00Z',
  },
  // ... các mock khác ...
]

const getStatusColor = (status: string) => {
  switch (status) {
    case 'completed':
      return 'bg-green-100 text-green-800'
    case 'pending':
      return 'bg-yellow-100 text-yellow-800'
    case 'failed':
      return 'bg-red-100 text-red-800'
    case 'refunded':
      return 'bg-blue-100 text-blue-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'completed':
      return 'Thành công'
    case 'pending':
      return 'Chờ xử lý'
    case 'failed':
      return 'Thất bại'
    case 'refunded':
      return 'Đã hoàn tiền'
    default:
      return status
  }
}

const getMethodText = (method: string) => {
  switch (method) {
    case 'direct':
      return 'Chuyển khoản'
    case 'online':
      return 'Online'
    case '9pay':
      return '9Pay'
    case 'momo':
      return 'Momo'
    case 'vnpay':
      return 'VNPay'
    default:
      return method
  }
}

const formatPrice = (price: number) =>
  new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(
    price,
  )

export default function AdminPayments() {
  const { isAdminLogin } = useAdminAuthContext()
  const router = useRouter()
  const [payments, setPayments] = useState<Payment[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [methodFilter, setMethodFilter] = useState('')

  useEffect(() => {
    if (typeof window !== 'undefined' && !isAdminLogin) {
      router.push('/login')
      return
    }
    setTimeout(() => {
      setPayments(mockPayments)
      setIsLoading(false)
    }, 500)
  }, [isAdminLogin, router])

  const filteredPayments = payments.filter((payment) => {
    const matchesSearch =
      payment.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.userEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.orderId.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = !statusFilter || payment.status === statusFilter
    const matchesMethod = !methodFilter || payment.method === methodFilter
    return matchesSearch && matchesStatus && matchesMethod
  })

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-semibold text-gray-900">
            Quản lý thanh toán
          </h1>
        </div>
        <div className="flex gap-4">
          <input
            type="text"
            placeholder="Tìm kiếm theo tên, email, mã đơn..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg"
          >
            <option value="">Tất cả trạng thái</option>
            <option value="completed">Thành công</option>
            <option value="pending">Chờ xử lý</option>
            <option value="failed">Thất bại</option>
            <option value="refunded">Đã hoàn tiền</option>
          </select>
          <select
            value={methodFilter}
            onChange={(e) => setMethodFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg"
          >
            <option value="">Tất cả phương thức</option>
            <option value="direct">Chuyển khoản</option>
            <option value="online">Online</option>
            <option value="9pay">9Pay</option>
            <option value="momo">Momo</option>
            <option value="vnpay">VNPay</option>
          </select>
        </div>
        <div className="bg-white shadow rounded-lg overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Mã đơn
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Khách hàng
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Email
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Số tiền
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Phương thức
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Trạng thái
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Thời gian
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredPayments.map((payment) => (
                <tr key={payment.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap font-mono">
                    {payment.orderId}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {payment.userName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {payment.userEmail}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {formatPrice(payment.amount)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getMethodText(payment.method)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(payment.status)}`}
                    >
                      {getStatusText(payment.status)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-xs text-gray-500">
                    {new Date(payment.createdAt).toLocaleString('vi-VN')}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </AdminLayout>
  )
}
