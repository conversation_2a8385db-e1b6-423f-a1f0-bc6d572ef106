name: Run test

on:
  pull_request:
    types:
      - opened
      - synchronize

jobs:
  test:
    runs-on: ubuntu-latest
    timeout-minutes: 20
    steps:
      - uses: actions/checkout@v2
      - name: Cancel Previous Runs
        uses: styfle/cancel-workflow-action@0.8.0
        with:
          access_token: ${{ github.token }}

      - name: Use Node.js 20.x
        uses: actions/setup-node@v3
        with:
          node-version: 20

      - uses: pnpm/action-setup@v2
        with:
          version: 9.0.6

      - name: Jest run
        run: |
          pnpm install
          pnpm test:ci
      - name: Cypress run
        uses: cypress-io/github-action@v4
        with:
          build: pnpm build
          start: pnpm start
