import Link from 'next/link'

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        <div className="mb-8">
          <h1 className="text-9xl font-bold text-gray-200">404</h1>
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Trang không tồn tại
          </h2>
          <p className="text-gray-600 mb-8">
            Xin lỗi, chúng tôi không thể tìm thấy trang bạn đang tìm kiếm.
          </p>
        </div>
        
        <div className="space-y-4">
          <Link
            href="/bookstore"
            className="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Về trang chủ
          </Link>
          
          <div className="text-sm text-gray-500">
            Hoặc{' '}
            <Link href="/bookstore" className="text-blue-600 hover:underline">
              xem sách
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
