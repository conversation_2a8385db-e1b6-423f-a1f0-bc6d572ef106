{"compilerOptions": {"target": "esnext", "module": "esnext", "jsx": "preserve", "lib": ["dom", "es2017"], "moduleResolution": "node", "allowJs": true, "noEmit": true, "strict": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "noUnusedLocals": true, "noUnusedParameters": true, "removeComments": false, "preserveConstEnums": true, "sourceMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "baseUrl": "./src/", "paths": {"@/*": ["./*"]}, "incremental": true, "plugins": [{"name": "next"}]}, "include": ["**.config.js", "**/*.ts", "**/*.tsx", "next-env.d.ts", "scripts/**/*.js", ".next/types/**/*.ts"], "exclude": ["node_modules"]}