'use client'

import Image from 'next/image'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'

import { AdminLayout } from '../../../components/Admin/Layout'
import { useAdminAuthContext } from '../../../context/adminAuth'

interface Book {
  id: string
  title: string
  description: string
  price: number
  categoryId: string
  categoryName: string
  programName: string
  author: string
  publisher: string
  isbn: string
  imageUrl: string
  isActive: boolean
  stock: number
  rating: number
  reviewCount: number
  createdAt: string
  updatedAt: string
}

// Mock data for books
const mockBooks: Book[] = [
  {
    id: '1',
    title: 'IB Mathematics Analysis and Approaches HL',
    description:
      'Comprehensive textbook for IB Mathematics Analysis and Approaches Higher Level',
    price: 450000,
    categoryId: '1',
    categoryName: 'Analysis and Approaches',
    programName: 'IB Mathematics',
    author: '<PERSON>, V<PERSON><PERSON>',
    publisher: 'Cambridge University Press',
    isbn: '978-1108440066',
    imageUrl: '/images/books/ib-math-aa-hl.jpg',
    isActive: true,
    stock: 25,
    rating: 4.5,
    reviewCount: 12,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
  },
  {
    id: '2',
    title: 'Cambridge IELTS 18 Academic',
    description: 'Official IELTS practice materials with authentic test papers',
    price: 320000,
    categoryId: '4',
    categoryName: 'Listening',
    programName: 'IELTS Preparation',
    author: 'Cambridge Assessment English',
    publisher: 'Cambridge University Press',
    isbn: '978-1316637456',
    imageUrl: '/images/books/cambridge-ielts-18.jpg',
    isActive: true,
    stock: 50,
    rating: 4.8,
    reviewCount: 28,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
  },
  {
    id: '3',
    title: 'IB Physics Course Book',
    description: 'Complete coverage of the IB Physics syllabus',
    price: 520000,
    categoryId: '3',
    categoryName: 'Mechanics',
    programName: 'IB Physics',
    author: 'Michael Bowen-Jones, David Homer',
    publisher: 'Oxford University Press',
    isbn: '978-0198307754',
    imageUrl: '/images/books/ib-physics.jpg',
    isActive: true,
    stock: 15,
    rating: 4.3,
    reviewCount: 8,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
  },
]

export default function AdminBooks() {
  const { isAdminLogin } = useAdminAuthContext()
  const router = useRouter()
  const [books, setBooks] = useState<Book[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')

  useEffect(() => {
    if (typeof window !== 'undefined' && !isAdminLogin) {
      router.push('/login')
      return
    }

    // Simulate API call
    setTimeout(() => {
      setBooks(mockBooks)
      setIsLoading(false)
    }, 1000)
  }, [isAdminLogin, router])

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(price)
  }

  const filteredBooks = books.filter((book) => {
    const matchesSearch =
      book.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      book.author.toLowerCase().includes(searchTerm.toLowerCase()) ||
      book.isbn.includes(searchTerm)
    const matchesCategory =
      !selectedCategory || book.categoryId === selectedCategory
    return matchesSearch && matchesCategory
  })

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">
              Quản lý sách
            </h1>
            <p className="text-sm text-gray-500">
              Quản lý danh sách sách trong hệ thống
            </p>
          </div>
          <button className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700">
            Thêm sách mới
          </button>
        </div>

        {/* Filters */}
        <div className="flex gap-4">
          <input
            type="text"
            placeholder="Tìm kiếm sách..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="">Tất cả danh mục</option>
            <option value="1">Analysis and Approaches</option>
            <option value="3">Mechanics</option>
            <option value="4">Listening</option>
          </select>
        </div>

        {/* Books Table */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sách
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Danh mục
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Giá
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tồn kho
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Trạng thái
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Thao tác
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredBooks.map((book) => (
                <tr key={book.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <Image
                          className="h-10 w-10 rounded-lg object-cover"
                          src={book.imageUrl}
                          alt={book.title}
                        />
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {book.title}
                        </div>
                        <div className="text-sm text-gray-500">
                          {book.author}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {book.categoryName}
                    </div>
                    <div className="text-sm text-gray-500">
                      {book.programName}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatPrice(book.price)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        book.stock > 10
                          ? 'bg-green-100 text-green-800'
                          : book.stock > 0
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {book.stock}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        book.isActive
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {book.isActive ? 'Hoạt động' : 'Không hoạt động'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button className="text-primary-600 hover:text-primary-900 mr-3">
                      Sửa
                    </button>
                    <button className="text-red-600 hover:text-red-900">
                      Xóa
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </AdminLayout>
  )
}
