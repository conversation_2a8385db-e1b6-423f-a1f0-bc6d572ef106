'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAdminAuthContext } from '../../../context/adminAuth'
import { AdminLayout } from '../../../components/Admin/Layout'

interface BookRating {
  id: string
  userId: string
  userName: string
  userEmail: string
  bookId: string
  bookTitle: string
  rating: number
  comment: string
  isApproved: boolean
  createdAt: string
  updatedAt: string
}

const mockRatings: BookRating[] = [
  {
    id: '1',
    userId: '1',
    userName: 'Nguyễn Văn A',
    userEmail: '<EMAIL>',
    bookId: '1',
    bookTitle: 'IB Mathematics Analysis and Approaches HL',
    rating: 5,
    comment:
      '<PERSON><PERSON>ch rất hay, giải thích chi tiết và dễ hiểu. Rất phù hợp cho học sinh IB.',
    isApproved: true,
    createdAt: '2024-01-20T14:30:00Z',
    updatedAt: '2024-01-20T14:30:00Z',
  },
  // ... các mock khác ...
]

const renderStars = (rating: number) => (
  <span className="flex items-center">
    {[...Array(5)].map((_, i) => (
      <svg
        key={i}
        className={`w-4 h-4 ${i < rating ? 'text-yellow-400' : 'text-gray-300'}`}
        fill="currentColor"
        viewBox="0 0 20 20"
      >
        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
      </svg>
    ))}
  </span>
)

export default function AdminRatings() {
  const { isAdminLogin } = useAdminAuthContext()
  const router = useRouter()
  const [ratings, setRatings] = useState<BookRating[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')

  useEffect(() => {
    if (typeof window !== 'undefined' && !isAdminLogin) {
      router.push('/login')
      return
    }
    setTimeout(() => {
      setRatings(mockRatings)
      setIsLoading(false)
    }, 500)
  }, [isAdminLogin, router])

  const filteredRatings = ratings.filter((rating) => {
    const matchesSearch =
      rating.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rating.userEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rating.bookTitle.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus =
      !statusFilter ||
      (statusFilter === 'approved' ? rating.isApproved : !rating.isApproved)
    return matchesSearch && matchesStatus
  })

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-semibold text-gray-900">
            Quản lý đánh giá sách
          </h1>
        </div>
        <div className="flex gap-4">
          <input
            type="text"
            placeholder="Tìm kiếm theo tên, email, tên sách..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          />
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg"
          >
            <option value="">Tất cả trạng thái</option>
            <option value="approved">Đã duyệt</option>
            <option value="pending">Chưa duyệt</option>
          </select>
        </div>
        <div className="bg-white shadow rounded-lg overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Người đánh giá
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Email
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sách
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Số sao
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Bình luận
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Trạng thái
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Thời gian
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredRatings.map((rating) => (
                <tr key={rating.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    {rating.userName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {rating.userEmail}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {rating.bookTitle}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {renderStars(rating.rating)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap max-w-xs truncate">
                    {rating.comment}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${rating.isApproved ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}
                    >
                      {rating.isApproved ? 'Đã duyệt' : 'Chưa duyệt'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-xs text-gray-500">
                    {new Date(rating.createdAt).toLocaleString('vi-VN')}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </AdminLayout>
  )
}
