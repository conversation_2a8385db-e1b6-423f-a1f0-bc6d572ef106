"use client"

import type React from "react"
import { CheckboxGroup } from "../../CheckboxGroup"
import { categories, gradeLevels } from "../../../data/books"

interface SidebarProps {
  selectedCategories: string[]
  selectedGradeLevels: string[]
  priceRange: [number, number]
  onCategoryChange: (categories: string[]) => void
  onGradeLevelChange: (gradeLevels: string[]) => void
  onPriceRangeChange: (range: [number, number]) => void
  onClearFilters: () => void
}

export const Sidebar: React.FC<SidebarProps> = ({
  selectedCategories,
  selectedGradeLevels,
  priceRange,
  onCategoryChange,
  onGradeLevelChange,
  onPriceRangeChange,
  onClearFilters,
}) => {
  const handlePriceChange = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const newRange = [...priceRange] as [number, number]
    newRange[index] = Number.parseInt(e.target.value) || 0
    onPriceRangeChange(newRange)
  }

  return (
    <div className="bg-white rounded-2xl shadow-lg p-6 sticky top-24">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-gray-900">Bộ lọc</h2>
        <button
          onClick={onClearFilters}
          className="text-sm text-blue-600 hover:text-blue-800 font-medium transition-colors"
        >
          Xóa tất cả
        </button>
      </div>

      <div className="space-y-8">
        {/* Categories */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
            Môn học
          </h3>
          <CheckboxGroup
            options={categories.map((cat) => ({ label: cat, value: cat }))}
            selectedValues={selectedCategories}
            onChange={onCategoryChange}
            className="space-y-3"
          />
        </div>

        {/* Grade Levels */}
        <div className="border-t border-gray-200 pt-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <span className="w-2 h-2 bg-purple-500 rounded-full mr-2"></span>
            Lớp học
          </h3>
          <CheckboxGroup
            options={gradeLevels.map((grade) => ({ label: grade, value: grade }))}
            selectedValues={selectedGradeLevels}
            onChange={onGradeLevelChange}
            className="space-y-3"
          />
        </div>

        {/* Price Range */}
        <div className="border-t border-gray-200 pt-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
            Khoảng giá
          </h3>
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <input
                type="number"
                placeholder="Từ"
                value={priceRange[0] || ""}
                onChange={(e) => handlePriceChange(e, 0)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <span className="text-gray-500">-</span>
              <input
                type="number"
                placeholder="Đến"
                value={priceRange[1] || ""}
                onChange={(e) => handlePriceChange(e, 1)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div className="text-sm text-gray-500">
              Giá từ {priceRange[0]?.toLocaleString() || 0}đ đến {priceRange[1]?.toLocaleString() || 500000}đ
            </div>
          </div>
        </div>

        {/* Quick Filters */}
        <div className="border-t border-gray-200 pt-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <span className="w-2 h-2 bg-orange-500 rounded-full mr-2"></span>
            Lọc nhanh
          </h3>
          <div className="space-y-2">
            <button className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
              📚 Sách bán chạy
            </button>
            <button className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
              ⭐ Đánh giá cao
            </button>
            <button className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
              🆕 Sách mới
            </button>
            <button className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
              💰 Giá tốt
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
